# Docker 环境下的 Celery 部署指南

## 📋 概述

本文档描述了如何在 Docker 环境中部署和管理基于 Celery+Redis 的异步协议解析服务。

## 🏗️ 架构组件

### 1. Circus 进程管理
- **主应用**: `yiya-ai-bot` (FastAPI 服务)
- **Celery Worker**: `yiya-ai-bot-celery` (协议解析任务处理)
- **Celery 监控**: `yiya-ai-bot-celery-monitor` (连接监控和自动恢复)

### 2. 关键文件
```
APP-META/docker-config/
├── environment/common/app/
│   ├── conf/circus.ini                    # Circus 配置
│   └── bin/
│       ├── appctl.sh                      # 应用控制脚本
│       └── celery_realtime_monitor.sh     # Celery 监控脚本
└── Dockerfile                             # Docker 镜像构建
```

## 🚀 部署步骤

### 1. 构建 Docker 镜像

**重要说明**: Dockerfile 已更新以支持 Celery+Redis 异步架构：
- ✅ 安装 Celery、Redis 等必要依赖
- ✅ 覆盖基础镜像中的旧配置文件
- ✅ 复制 Celery 监控脚本并设置执行权限

```bash
# 在项目根目录执行
docker build -f APP-META/docker-config/Dockerfile -t yiya-ai-bot:protocol-async .
```

### 2. 启动容器
```bash
docker run -d \
  --name yiya-ai-bot-protocol \
  -p 7860:7860 \
  -e REDIS_HOST=your-redis-host \
  -e REDIS_PORT=6379 \
  -e REDIS_PASSWORD=your-redis-password \
  yiya-ai-bot:protocol-async
```

### 3. 验证部署
```bash
# 检查应用状态
docker exec yiya-ai-bot-protocol /root/yiya-ai-bot/bin/appctl.sh status

# 检查 Celery 状态
docker exec yiya-ai-bot-protocol /root/yiya-ai-bot/bin/appctl.sh celery-status

# 执行 Celery 连接检查
docker exec yiya-ai-bot-protocol /root/yiya-ai-bot/bin/appctl.sh celery-check
```

## 🔧 管理命令

### appctl.sh 新增命令

#### 基础命令
```bash
# 启动服务（带日志跟踪）
./bin/appctl.sh start

# 启动服务（不跟踪日志）
./bin/appctl.sh start-no-tail

# 停止服务
./bin/appctl.sh stop

# 重启服务
./bin/appctl.sh restart

# 查看状态
./bin/appctl.sh status
```

#### Celery 专用命令
```bash
# 查看 Celery Worker 状态
./bin/appctl.sh celery-status

# 重启 Celery Worker
./bin/appctl.sh celery-restart

# 执行 Celery 连接检查
./bin/appctl.sh celery-check
```

## 📊 监控机制

### 1. 实时监控
- **监控脚本**: `celery_realtime_monitor.sh`
- **检查间隔**: 30秒
- **失败阈值**: 连续2次失败自动重启
- **监控项目**:
  - Worker 响应性
  - Redis 连接状态
  - 队列状态

### 2. 监控日志
```bash
# 查看监控日志
tail -f /root/yiya-ai-bot/logs/celery_realtime_monitor.log

# 查看 Celery Worker 日志
tail -f /root/yiya-ai-bot/logs/celery.log

# 查看 Celery 错误日志
tail -f /root/yiya-ai-bot/logs/celery_error.log
```

### 3. 健康检查
监控脚本执行以下检查：
- **Worker 注册检查**: 验证 Worker 是否在 Celery 中注册
- **Redis 连接检查**: 测试 broker 和 result backend 连接
- **队列状态检查**: 监控活跃任务和队列长度

## 🔄 自动恢复机制

### 1. 连接断开处理
- 检测到 Redis 连接断开时自动重启 Worker
- 使用 `circusctl restart` 进行优雅重启
- 备用方案：发送 SIGTERM 信号，依赖 Circus 自动拉起

### 2. Worker 无响应处理
- 检测到 Worker 无响应时立即重启
- 重启后等待30秒让服务稳定
- 重置失败计数器

### 3. 进程监控配置
```ini
# circus.ini 中的关键配置
autostart=true          # 自动启动
autorestart=true        # 自动重启
restart_delay=10        # 重启延迟
max_age=3600           # 最大运行时间
check_flapping=true    # 检查频繁重启
flapping_attempts=3    # 重启尝试次数
flapping_window=180    # 重启时间窗口
```

## 🐛 故障排查

### 1. 常见问题

#### Celery Worker 无法启动
```bash
# 检查 Redis 连接
docker exec yiya-ai-bot-protocol python3.11 -c "
from celery_task.celery import celery_app
broker = celery_app.connection()
broker.connect()
print('Redis 连接正常')
"

# 检查配置
docker exec yiya-ai-bot-protocol python3.11 -c "
from config.settings import REDIS_HOST, REDIS_PORT, REDIS_DB
print(f'Redis 配置: {REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}')
"
```

#### Worker 注册但无响应
```bash
# 手动重启 Worker
docker exec yiya-ai-bot-protocol /root/yiya-ai-bot/bin/appctl.sh celery-restart

# 检查进程状态
docker exec yiya-ai-bot-protocol ps aux | grep celery
```

#### 任务提交失败
```bash
# 检查队列状态
docker exec yiya-ai-bot-protocol /root/yiya-ai-bot/bin/appctl.sh celery-check

# 查看任务日志
docker exec yiya-ai-bot-protocol tail -f /root/yiya-ai-bot/logs/celery.log
```

### 2. 日志分析

#### 关键日志文件
- `/root/yiya-ai-bot/logs/application.log` - 主应用日志
- `/root/yiya-ai-bot/logs/celery.log` - Celery Worker 日志
- `/root/yiya-ai-bot/logs/celery_error.log` - Celery 错误日志
- `/root/yiya-ai-bot/logs/celery_realtime_monitor.log` - 监控日志

#### 日志关键词
- `✅` - 正常状态
- `❌` - 错误状态
- `⚠️` - 警告状态
- `🔄` - 重启操作
- `🚨` - 紧急情况

## 🔒 安全考虑

### 1. Redis 连接安全
- 使用密码认证
- 配置防火墙规则
- 使用专用数据库 (db4)

### 2. 容器安全
- 使用非 root 用户运行（如果可能）
- 限制容器权限
- 定期更新基础镜像

## 📈 性能优化

### 1. Celery Worker 配置
```ini
# 推荐配置
--pool=threads          # 使用线程池
--concurrency=4         # 并发数
--time-limit=300        # 任务超时时间
--soft-time-limit=240   # 软超时时间
--max-tasks-per-child=100  # 每个进程最大任务数
--prefetch-multiplier=1    # 预取倍数
```

### 2. 监控优化
- 调整检查间隔（默认30秒）
- 调整失败阈值（默认2次）
- 优化健康检查超时时间

## 🔄 升级和维护

### 1. 滚动升级
```bash
# 停止旧容器
docker stop yiya-ai-bot-protocol

# 启动新容器
docker run -d --name yiya-ai-bot-protocol-new ...

# 验证新容器
docker exec yiya-ai-bot-protocol-new /root/yiya-ai-bot/bin/appctl.sh celery-check

# 删除旧容器
docker rm yiya-ai-bot-protocol
```

### 2. 配置更新
- 修改配置文件后需要重启相应服务
- 使用 `celery-restart` 命令重启 Worker
- 监控日志确保服务正常

## 📞 支持

如遇问题，请检查：
1. 日志文件中的错误信息
2. Redis 连接状态
3. Worker 注册状态
4. 网络连接情况

提供以下信息以便排查：
- 错误日志内容
- `celery-status` 命令输出
- `celery-check` 命令输出
- 容器运行状态
