# 本地测试指南

## 🚀 快速启动

### 方法一：使用 Circus 进程管理器（推荐）

```bash
# 1. 激活虚拟环境
source .venv/bin/activate

# 2. 启动所有服务（FastAPI + Celery Worker）
circusd circus_local.ini

# 3. 查看服务状态
circusctl status

# 4. 停止所有服务
circusctl quit
```

### 方法二：分别启动各个服务

#### 启动 Redis（如果没有运行）
```bash
# macOS 使用 Homebrew
brew services start redis

# 或者直接运行
redis-server
```

#### 启动 FastAPI 应用
```bash
# 终端 1
source .venv/bin/activate
uvicorn app:app --host 127.0.0.1 --port 7860 --reload
```

#### 启动 Celery Worker
```bash
# 终端 2
source .venv/bin/activate
celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4
```

#### 启动 Celery Flower 监控（可选）
```bash
# 终端 3
source .venv/bin/activate
celery -A celery_task flower --port=5555
```

## 🧪 测试命令

### 1. 基础健康检查
```bash
# 检查 FastAPI 应用
curl http://127.0.0.1:7860/health

# 检查 Redis 连接
redis-cli ping
```

### 2. 运行异步协议解析测试
```bash
source .venv/bin/activate
python test_protocol_async.py
```

### 3. 运行完整场景测试
```bash
source .venv/bin/activate
python test_complete_scenario.py
```

### 4. 测试并发任务处理
```bash
source .venv/bin/activate
python test_concurrent_tasks.py
```

## 📊 监控和调试

### Circus 管理命令
```bash
# 查看所有进程状态
circusctl status

# 重启 FastAPI 应用
circusctl restart yiya-ai-bot

# 重启 Celery Worker
circusctl restart yiya-ai-bot-celery

# 查看进程日志
circusctl stats yiya-ai-bot
circusctl stats yiya-ai-bot-celery

# 停止特定进程
circusctl stop yiya-ai-bot-celery

# 启动特定进程
circusctl start yiya-ai-bot-celery
```

### 日志查看
```bash
# FastAPI 应用日志
tail -f logs/application.log

# Celery Worker 日志
tail -f logs/celery.log

# Celery 错误日志
tail -f logs/celery_error.log

# 实时查看所有日志
tail -f logs/*.log
```

### Celery 监控命令
```bash
# 查看 Worker 状态
celery -A celery_task inspect active

# 查看队列状态
celery -A celery_task inspect reserved

# 查看注册的任务
celery -A celery_task inspect registered

# 清空队列
celery -A celery_task purge

# 查看 Worker 统计信息
celery -A celery_task inspect stats
```

## 🔧 故障排除

### 常见问题

1. **Redis 连接失败**
   ```bash
   # 检查 Redis 是否运行
   redis-cli ping
   
   # 如果没有运行，启动 Redis
   brew services start redis
   ```

2. **Celery Worker 无法启动**
   ```bash
   # 检查 Python 路径
   echo $PYTHONPATH
   
   # 手动设置路径
   export PYTHONPATH=/Users/<USER>/PycharmProjects/yiya-ai-bot:$PYTHONPATH
   ```

3. **任务执行失败**
   ```bash
   # 查看详细错误日志
   tail -f logs/celery_error.log
   
   # 检查任务状态
   celery -A celery_task inspect active
   ```

4. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :7860  # FastAPI
   lsof -i :5555  # Circus/Flower
   lsof -i :6379  # Redis
   ```

## 🧪 API 测试示例

### 异步协议解析
```bash
# 提交异步任务
curl -X POST "http://127.0.0.1:7860/extract-by-docx-async" \
  -H "Content-Type: application/json" \
  -d '{
    "file_key": "test-protocol.docx",
    "file_name": "test-protocol.docx",
    "extract_keys_only": false
  }'

# 查询任务状态（使用返回的 task_id）
curl "http://127.0.0.1:7860/task-status/your-task-id-here"
```

### 同步协议解析（向后兼容）
```bash
curl -X POST "http://127.0.0.1:7860/extract-by-docx" \
  -H "Content-Type: application/json" \
  -d '{
    "file_key": "test-protocol.docx",
    "file_name": "test-protocol.docx",
    "extract_keys_only": false
  }'
```

## 📈 性能监控

### 使用 Flower 监控 Celery
1. 启动 Flower：`celery -A celery_task flower --port=5555`
2. 访问：http://127.0.0.1:5555
3. 查看任务执行情况、Worker 状态等

### 使用 Redis CLI 监控
```bash
# 监控 Redis 命令
redis-cli monitor

# 查看内存使用
redis-cli info memory

# 查看连接数
redis-cli info clients
```

## 🎯 开发建议

1. **使用 Circus**：推荐使用 `circusd circus_local.ini` 启动，可以统一管理所有进程
2. **日志监控**：开发时保持 `tail -f logs/*.log` 运行，实时查看日志
3. **代码热重载**：FastAPI 支持 `--reload` 参数，代码修改后自动重启
4. **测试先行**：修改代码后先运行 `test_protocol_async.py` 确保功能正常
5. **性能监控**：使用 Flower 监控 Celery 任务执行情况

## 🔄 完整开发流程

```bash
# 1. 启动开发环境
source .venv/bin/activate
circusd circus_local.ini

# 2. 在另一个终端监控日志
tail -f logs/*.log

# 3. 运行测试
python test_protocol_async.py

# 4. 开发完成后停止服务
circusctl quit
```
