from config.settings import settings

broker_protocol = 'redis'
broker_host = settings.REDIS_HOST
broker_port = settings.REDIS_PORT
broker_db = settings.REDIS_DB
broker_pwd = settings.REDIS_PASSWORD

result_db = settings.REDIS_TASK_DB

# 构建Redis URL（不在URL中包含连接参数，而是通过transport_options配置）
broker_url = "{}://:{}@{}:{}/{}".format(
    broker_protocol,
    broker_pwd,
    broker_host,
    broker_port,
    broker_db)

result_backend = "{}://:{}@{}:{}/{}".format(
    broker_protocol,
    broker_pwd,
    broker_host,
    broker_port,
    result_db)

# 任务序列化格式
task_serializer = 'json'
# 结果序列化格式
result_serializer = 'json'
# 接受的内容类型
accept_content = ['json']
# 时区
timezone = 'Asia/Shanghai'
# 启用UTC
enable_utc = True

#任务结果失效时间
result_expire =settings.TASK_RESULT_EXPIRE_TIME

# 设置连接超时和读取超时
broker_transport_options = {
    'socket_connect_timeout': 30,    # 连接超时，单位：秒
    'socket_timeout': 60.0,          # 读取超时，单位：秒（增加到60秒）
    'socket_keepalive': True,        # 启用 TCP keepalive
    # 注释掉socket_keepalive_options，因为某些Redis版本不支持
    # 'socket_keepalive_options': {    # TCP keepalive 详细配置
    #     'TCP_KEEPIDLE': 600,         # 开始发送keepalive探测前的空闲时间
    #     'TCP_KEEPINTVL': 30,         # keepalive探测间隔
    #     'TCP_KEEPCNT': 3,            # 最大keepalive探测次数
    # },
    'retry_on_timeout': True,        # 超时时重试
    'health_check_interval': 30,     # 健康检查间隔（秒）
    'retry_on_error': [ConnectionError, TimeoutError],  # 遇到这些错误时重试
    'max_connections': 50,           # 连接池最大连接数
    'connection_pool_kwargs': {      # 连接池额外配置
        'max_connections': 50,
        'retry_on_timeout': True,
        'socket_connect_timeout': 30,
        'socket_timeout': 60,
        'socket_keepalive': True,
    }
}

# 连接池配置
broker_connection_retry_on_startup = True
broker_connection_retry = True
broker_connection_max_retries = 10

# 任务确认配置
task_acks_late = True
worker_prefetch_multiplier = 1

# 连接丢失时的任务处理（解决警告信息）
worker_cancel_long_running_tasks_on_connection_loss = True

# 结果后端连接配置
result_backend_transport_options = broker_transport_options

# 使用自定义队列名，避免与其他项目冲突
task_default_queue = 'protocol_parse_queue'
task_routes = {
    'extract_protocol_task': {'queue': 'protocol_parse_queue'},
}
