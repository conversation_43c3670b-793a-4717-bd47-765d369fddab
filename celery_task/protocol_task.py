# celery_task/protocol_task.py

import io
import time
import random
import orj<PERSON>
import gc
from datetime import datetime
from typing import Dict, Any
from celery import Task

from celery_task.celery import celery_app
from logger.logger import app_logger
from utils.oss_utils import download_file_to_stream, write_large_file_from_stream, write_large_file_from_stream_parallel
from utils.protocol_utils import extract_structured_protocol
from oss2.exceptions import NoSuchKey


def safe_update_state(task_instance, state, meta, max_retries=3):
    """
    安全的状态更新函数，带重试机制
    """
    for attempt in range(max_retries):
        try:
            task_instance.update_state(state=state, meta=meta)
            return True
        except Exception as e:
            app_logger.warning(f"状态更新失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                app_logger.error(f"状态更新最终失败，跳过此次更新")
                return False
            time.sleep(1)  # 等待1秒后重试
    return False


@celery_app.task(
    name="extract_protocol_task",
    bind=True,
    autoretry_for=(ConnectionError, TimeoutError),
    retry_kwargs={'max_retries': 2, 'countdown': 60}
)
def extract_protocol_task(self: Task, file_key: str, extract_keys_only: bool = False) -> Dict[str, Any]:
    """
    协议文档解析任务：
    1. 从 OSS 下载 .docx 文件
    2. 解析出层级化 JSON 结构
    3. 将 sections 部分上传回 OSS
    4. 返回 key_information 和 OSS 路径
    """
    task_id = self.request.id
    app_logger.info(f"开始执行协议解析任务，Task ID: {task_id}, File Key: {file_key}")
    
    global_start_time = time.time()
    stage_times = {}
    
    # 初始化任务状态
    task_info = {
        "task_id": task_id,
        "file_key": file_key,
        "extract_keys_only": extract_keys_only,
        "current_stage": "初始化",
        "progress": 0,
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    try:
        # 阶段1: 下载文件
        stage_start = time.time()
        task_info.update({
            "current_stage": "下载 DOCX 文件",
            "progress": 10,
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        safe_update_state(self, "PROGRESS", task_info)
        
        app_logger.info(f"正在从OSS下载文件: {file_key}")
        oss_stream = download_file_to_stream(file_key)
        download_time = time.time() - stage_start
        stage_times['download'] = download_time
        app_logger.info(f"文件下载完成，耗时: {download_time:.2f}s")

        # 阶段2: 处理文件（解析docx）
        stage_start = time.time()
        task_info.update({
            "current_stage": "解析 DOCX 文档",
            "progress": 30,
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        safe_update_state(self, "PROGRESS", task_info)
        
        with io.BytesIO(oss_stream.read()) as docx_stream:
            if hasattr(oss_stream, 'close'):
                oss_stream.close()
            protocol_data = extract_structured_protocol(docx_stream, extract_keys_only=extract_keys_only)
        
        process_time = time.time() - stage_start
        stage_times['process'] = process_time
        app_logger.info(f"文档解析完成，耗时: {process_time:.2f}s")

        # 将解析结果分离：key_information 用于API直接返回，sections 用于上传OSS
        key_information = protocol_data.get("key_information", {})
        sections_to_upload = protocol_data.get("sections", [])

        # 阶段3: JSON序列化
        stage_start = time.time()
        task_info.update({
            "current_stage": "序列化数据",
            "progress": 60,
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        safe_update_state(self, "PROGRESS", task_info)

        # 创建一个只包含 sections 键的新字典，以确保上传的JSON文件保留根键
        data_for_oss = {"sections": sections_to_upload}
        json_bytes = orjson.dumps(data_for_oss)
        
        serialize_time = time.time() - stage_start
        stage_times['serialize'] = serialize_time

        json_size_mb = len(json_bytes) / (1024 * 1024)
        app_logger.info(f"JSON序列化完成，生成 {json_size_mb:.2f}MB 数据，耗时: {serialize_time:.2f}s")

        # 内存清理
        del protocol_data
        del sections_to_upload
        del data_for_oss
        gc.collect()

        # 阶段4: 上传OSS
        stage_start = time.time()
        task_info.update({
            "current_stage": "上传到 OSS",
            "progress": 80,
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        safe_update_state(self, "PROGRESS", task_info)
        
        timestamp_ms = int(time.time() * 1000)
        random_suffix = random.randint(100000, 999999)
        oss_json_key = f"protocols/oss-{timestamp_ms}-{random_suffix}.json"

        with io.BytesIO(json_bytes) as json_stream:
            if json_size_mb > 50:
                write_large_file_from_stream_parallel(
                    json_stream,
                    oss_json_key,
                    part_size=20 * 1024 * 1024,
                    max_workers=2
                )
            else:
                write_large_file_from_stream(json_stream, oss_json_key)

        upload_time = time.time() - stage_start
        stage_times['upload'] = upload_time
        app_logger.info(f"上传OSS完成，耗时: {upload_time:.2f}s")

        # 清理内存
        del json_bytes
        gc.collect()

        # 计算总耗时和性能统计
        total_time = time.time() - global_start_time
        stage_times['total'] = total_time

        # 性能报告（保留2位小数）
        performance_report = {
            'stage_times': {k: round(v, 2) for k, v in stage_times.items()},
            'file_size_mb': round(json_size_mb, 2),
            'throughput': {
                'download': round(json_size_mb / download_time, 2) if 'download' in stage_times and download_time > 0 else 0,
                'upload': round(json_size_mb / upload_time, 2) if 'upload' in stage_times and upload_time > 0 else 0
            }
        }

        app_logger.info(f"性能统计 - 总耗时: {round(total_time, 2)}s, "
                        f"下载: {round(stage_times.get('download', 0), 2)}s, "
                        f"处理: {round(stage_times.get('process', 0), 2)}s, "
                        f"序列化: {round(stage_times.get('serialize', 0), 2)}s, "
                        f"上传: {round(stage_times.get('upload', 0), 2)}s")

        # 最终结果
        final_result = {
            "task_id": task_id,
            "status": "success",
            "progress": 100,
            "current_stage": "完成",
            "file_key": file_key,
            "extract_keys_only": extract_keys_only,
            "oss_key": oss_json_key,
            "key_information": key_information,
            "performance_report": performance_report,
            "total_time_seconds": round(total_time, 2),
            "created_at": task_info["created_at"],
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "completed_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        app_logger.info(f"协议解析任务完成，Task ID: {task_id}, 耗时: {total_time:.2f}秒")
        return final_result
        
    except NoSuchKey:
        error_msg = f"OSS文件未找到 (NoSuchKey)，请求的Key为: '{file_key}'"
        app_logger.warning(error_msg)
        raise Exception(error_msg)
        
    except Exception as e:
        error_msg = f"协议解析任务执行失败: {str(e)}"
        app_logger.error(error_msg, exc_info=True)
        raise Exception(error_msg)
