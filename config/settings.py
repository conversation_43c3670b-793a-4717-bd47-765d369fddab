from pydantic_settings import BaseSettings
import os

class Settings(BaseSettings):
    REDIS_HOST: str = "***********"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 4  # 使用 db4 作为协议解析专用 broker
    REDIS_TASK_DB:  int = 4  # 使用 db4 作为结果存储
    REDIS_PASSWORD: str = "wtg2024@"

    REDIS_DB_LOCAL: int = 4  # 本地环境也使用 db4
    REDIS_TASK_DB_LOCAL: int = 4

    # 任务配置
    TASK_RESULT_EXPIRE_TIME: int = 60 * 60 * 24  # 24小时
    MAX_CONCURRENT_TASKS: int = 10
    TASK_TIMEOUT: int = 3600
    MAX_RETRY_TIMES: int = 3

    # 协议解析相关配置
    PROTOCOL_PARSE_FOLDER: str = 'protocol_parse_files'
    if not os.path.exists(PROTOCOL_PARSE_FOLDER):
        os.makedirs(PROTOCOL_PARSE_FOLDER)

settings = Settings()
