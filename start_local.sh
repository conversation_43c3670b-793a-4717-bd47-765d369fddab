#!/bin/bash

# 本地开发环境启动脚本

set -e

echo "🚀 启动本地开发环境..."

# 检查虚拟环境
if [ ! -d ".venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行: uv venv"
    exit 1
fi

# 激活虚拟环境
source .venv/bin/activate

# 检查 Redis 是否运行
if ! redis-cli ping > /dev/null 2>&1; then
    echo "⚠️  Redis 未运行，尝试启动..."
    if command -v brew > /dev/null 2>&1; then
        brew services start redis
        sleep 2
    else
        echo "❌ 请手动启动 Redis: redis-server"
        exit 1
    fi
fi

# 检查 Redis 连接
if redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis 连接正常"
else
    echo "❌ Redis 连接失败"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动 Circus
echo "🎪 启动 Circus 进程管理器..."
echo "📊 监控地址: http://127.0.0.1:7860"
echo "📋 管理命令: circusctl status"
echo "🛑 停止服务: circusctl quit"
echo ""

circusd circus_local.ini
